<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search - WebToolsKit</title>
    <meta name="description" content="Search through WebToolsKit's tools and blog posts. Find the perfect tool or article for your needs.">
    <meta name="robots" content="noindex, follow">
    
    <!-- Schema.org markup for search page -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Search - WebToolsKit",
        "description": "Search through WebToolsKit's tools and blog posts",
        "url": "https://www.webtoolskit.org/p/search.html",
        "isPartOf": {
            "@type": "WebSite",
            "name": "WebToolsKit",
            "url": "https://www.webtoolskit.org"
        }
    }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .search-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .search-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .search-subtitle {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 30px;
        }

        .search-box {
            position: relative;
            max-width: 600px;
            margin: 0 auto 40px;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            font-size: 1.1rem;
            border: 2px solid #e1e8ed;
            border-radius: 50px;
            outline: none;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 1.2rem;
        }

        .search-results {
            margin-top: 30px;
        }

        .results-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .results-grid {
            display: grid;
            gap: 20px;
        }

        .result-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border-left: 4px solid #007bff;
        }

        .result-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .result-title a {
            color: #2c3e50;
            text-decoration: none;
        }

        .result-title a:hover {
            color: #007bff;
        }

        .result-description {
            color: #666;
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .result-meta {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
            color: #888;
        }

        .result-label {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            background: white;
            border-radius: 8px;
        }

        .result-thumbnail {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            margin-right: 15px;
            float: left;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .search-title {
                font-size: 2rem;
            }
            
            .search-input {
                padding: 12px 45px 12px 15px;
                font-size: 1rem;
            }
            
            .result-item {
                padding: 15px;
            }
            
            .result-thumbnail {
                width: 50px;
                height: 50px;
            }
        }

        .highlight {
            background-color: #fff3cd;
            padding: 1px 2px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-header">
            <h1 class="search-title">Search WebToolsKit</h1>
            <p class="search-subtitle">Find tools and articles to help with your projects</p>
            
            <div class="search-box">
                <input type="text" class="search-input" id="searchInput" placeholder="Search for tools, articles, or topics..." autocomplete="off">
                <i class="search-icon">🔍</i>
            </div>
        </div>

        <div class="search-results" id="searchResults">
            <div class="loading" id="loadingMessage">
                Start typing to search through our tools and articles...
            </div>
        </div>
    </div>

    <script>
        class WebToolsKitSearch {
            constructor() {
                this.searchInput = document.getElementById('searchInput');
                this.searchResults = document.getElementById('searchResults');
                this.loadingMessage = document.getElementById('loadingMessage');
                
                this.toolsData = [];
                this.postsData = [];
                this.isLoading = false;
                
                this.init();
            }

            init() {
                // Get search query from URL if present
                const urlParams = new URLSearchParams(window.location.search);
                const query = urlParams.get('q');
                if (query) {
                    this.searchInput.value = query;
                    this.performSearch(query);
                }

                // Add search event listener with debouncing
                let searchTimeout;
                this.searchInput.addEventListener('input', (e) => {
                    clearTimeout(searchTimeout);
                    const query = e.target.value.trim();
                    
                    if (query.length === 0) {
                        this.showInitialMessage();
                        return;
                    }
                    
                    if (query.length < 2) return;
                    
                    searchTimeout = setTimeout(() => {
                        this.performSearch(query);
                    }, 300);
                });

                // Load initial data
                this.loadData();
            }

            async loadData() {
                if (this.isLoading) return;
                this.isLoading = true;

                try {
                    // Load blog posts from Blogger JSON feed
                    await this.loadBlogPosts();
                    
                    // Load tools data (static pages)
                    await this.loadToolsData();
                    
                } catch (error) {
                    console.error('Error loading data:', error);
                } finally {
                    this.isLoading = false;
                }
            }

            async loadBlogPosts() {
                try {
                    // Use Blogger's JSON feed to get posts
                    const response = await fetch('/feeds/posts/default?alt=json&max-results=500');
                    const data = await response.json();
                    
                    this.postsData = data.feed.entry ? data.feed.entry.map(entry => ({
                        title: entry.title.$t,
                        url: entry.link.find(link => link.rel === 'alternate').href,
                        content: entry.content ? entry.content.$t : (entry.summary ? entry.summary.$t : ''),
                        published: entry.published.$t,
                        labels: entry.category ? entry.category.map(cat => cat.term) : [],
                        thumbnail: this.extractThumbnail(entry)
                    })) : [];
                } catch (error) {
                    console.error('Error loading blog posts:', error);
                    this.postsData = [];
                }
            }

            extractThumbnail(entry) {
                // Try to extract thumbnail from media or content
                if (entry.media$thumbnail) {
                    return entry.media$thumbnail.url;
                }

                // Extract from content
                const content = entry.content ? entry.content.$t : (entry.summary ? entry.summary.$t : '');
                const imgMatch = content.match(/<img[^>]+src="([^"]+)"/);
                return imgMatch ? imgMatch[1] : null;
            }

            async loadToolsData() {
                // Define tools data manually since they're static pages
                this.toolsData = [
                    // SEO Tools
                    { title: "Meta Tag Generator", url: "/p/meta-tag-generator.html", description: "Generate SEO-optimized meta tags for your website", category: "SEO Tools" },
                    { title: "Open Graph Tag Generator", url: "/p/open-graph-tag-generator.html", description: "Create Open Graph meta tags for social media sharing", category: "SEO Tools" },
                    { title: "Robots.txt Generator", url: "/p/robots-txt-generator.html", description: "Generate robots.txt file for search engine crawlers", category: "SEO Tools" },
                    { title: "Sitemap Generator", url: "/p/sitemap-generator.html", description: "Create XML sitemaps for better search engine indexing", category: "SEO Tools" },
                    { title: "Canonical URL Generator", url: "/p/canonical-url-generator.html", description: "Generate canonical URL tags to prevent duplicate content", category: "SEO Tools" },
                    { title: "Alt Text Checker", url: "/p/alt-text-checker.html", description: "Check and optimize alt text for images on your website", category: "SEO Tools" },
                    { title: "Keyword Density Checker", url: "/p/keyword-density-checker.html", description: "Analyze keyword density in your content for SEO optimization", category: "SEO Tools" },
                    { title: "H1-H6 Heading Checker", url: "/p/h1-h6-heading-checker.html", description: "Analyze heading structure for better SEO and accessibility", category: "SEO Tools" },
                    { title: "Title Meta Description Checker", url: "/p/title-meta-description-checker.html", description: "Check and optimize page titles and meta descriptions", category: "SEO Tools" },
                    { title: "URL SEO Analyzer", url: "/p/url-seo-analyzer.html", description: "Analyze URLs for SEO best practices and optimization", category: "SEO Tools" },

                    // Text Content Tools
                    { title: "Text Case Converter", url: "/p/text-case-converter.html", description: "Convert text between different cases (uppercase, lowercase, title case)", category: "Text Tools" },
                    { title: "Word Counter", url: "/p/word-counter.html", description: "Count words, characters, and paragraphs in your text", category: "Text Tools" },
                    { title: "Text Formatter", url: "/p/text-formatter.html", description: "Format and clean up text with various options", category: "Text Tools" },
                    { title: "Lorem Ipsum Generator", url: "/p/lorem-ipsum-generator.html", description: "Generate placeholder text for design and development", category: "Text Tools" },
                    { title: "Text Difference Checker", url: "/p/text-difference-checker.html", description: "Compare two texts and highlight differences", category: "Text Tools" },

                    // Development Tools
                    { title: "HTML Formatter", url: "/p/html-formatter.html", description: "Format and beautify HTML code", category: "Development Tools" },
                    { title: "CSS Formatter", url: "/p/css-formatter.html", description: "Format and beautify CSS code", category: "Development Tools" },
                    { title: "JavaScript Formatter", url: "/p/javascript-formatter.html", description: "Format and beautify JavaScript code", category: "Development Tools" },
                    { title: "JSON Formatter", url: "/p/json-formatter.html", description: "Format and validate JSON data", category: "Development Tools" },
                    { title: "Base64 Encoder Decoder", url: "/p/base64-encoder-decoder.html", description: "Encode and decode Base64 strings", category: "Development Tools" },
                    { title: "URL Encoder Decoder", url: "/p/url-encoder-decoder.html", description: "Encode and decode URLs for web development", category: "Development Tools" },
                    { title: "Color Picker", url: "/p/color-picker.html", description: "Pick colors and get hex, RGB, HSL values", category: "Development Tools" },

                    // Unit Converters
                    { title: "Length Converter", url: "/p/length-converter.html", description: "Convert between different length units", category: "Unit Converters" },
                    { title: "Weight Converter", url: "/p/weight-converter.html", description: "Convert between different weight units", category: "Unit Converters" },
                    { title: "Temperature Converter", url: "/p/temperature-converter.html", description: "Convert between Celsius, Fahrenheit, and Kelvin", category: "Unit Converters" },
                    { title: "Currency Converter", url: "/p/currency-converter.html", description: "Convert between different currencies", category: "Unit Converters" },

                    // Online Calculators
                    { title: "Percentage Calculator", url: "/p/percentage-calculator.html", description: "Calculate percentages, percentage increase/decrease", category: "Calculators" },
                    { title: "BMI Calculator", url: "/p/bmi-calculator.html", description: "Calculate Body Mass Index and health status", category: "Calculators" },
                    { title: "Age Calculator", url: "/p/age-calculator.html", description: "Calculate age in years, months, and days", category: "Calculators" },
                    { title: "Loan Calculator", url: "/p/loan-calculator.html", description: "Calculate loan payments and interest", category: "Calculators" },

                    // Image Tools
                    { title: "Image Resizer", url: "/p/image-resizer.html", description: "Resize images online without losing quality", category: "Image Tools" },
                    { title: "Image Compressor", url: "/p/image-compressor.html", description: "Compress images to reduce file size", category: "Image Tools" },
                    { title: "Image Format Converter", url: "/p/image-format-converter.html", description: "Convert images between different formats", category: "Image Tools" },

                    // Other Tools
                    { title: "QR Code Generator", url: "/p/qr-code-generator.html", description: "Generate QR codes for text, URLs, and more", category: "Other Tools" },
                    { title: "Password Generator", url: "/p/password-generator.html", description: "Generate strong, secure passwords", category: "Other Tools" },
                    { title: "Hash Generator", url: "/p/hash-generator.html", description: "Generate MD5, SHA1, SHA256 hashes", category: "Other Tools" },
                    { title: "UUID Generator", url: "/p/uuid-generator.html", description: "Generate unique identifiers (UUIDs)", category: "Other Tools" }
                ];
            }

            performSearch(query) {
                if (!query || query.length < 2) {
                    this.showInitialMessage();
                    return;
                }

                this.showLoading();

                // Search tools and posts
                const toolResults = this.searchTools(query);
                const postResults = this.searchPosts(query);

                this.displayResults(toolResults, postResults, query);
            }

            searchTools(query) {
                const queryLower = query.toLowerCase();
                return this.toolsData.filter(tool => {
                    return tool.title.toLowerCase().includes(queryLower) ||
                           tool.description.toLowerCase().includes(queryLower) ||
                           tool.category.toLowerCase().includes(queryLower);
                }).slice(0, 20); // Limit to 20 results
            }

            searchPosts(query) {
                const queryLower = query.toLowerCase();
                return this.postsData.filter(post => {
                    const contentText = this.stripHtml(post.content).toLowerCase();
                    return post.title.toLowerCase().includes(queryLower) ||
                           contentText.includes(queryLower) ||
                           post.labels.some(label => label.toLowerCase().includes(queryLower));
                }).slice(0, 20); // Limit to 20 results
            }

            stripHtml(html) {
                const div = document.createElement('div');
                div.innerHTML = html;
                return div.textContent || div.innerText || '';
            }

            highlightText(text, query) {
                if (!query) return text;
                const regex = new RegExp(`(${query})`, 'gi');
                return text.replace(regex, '<span class="highlight">$1</span>');
            }

            displayResults(toolResults, postResults, query) {
                let html = '';

                if (toolResults.length === 0 && postResults.length === 0) {
                    html = `
                        <div class="no-results">
                            <h3>No results found for "${query}"</h3>
                            <p>Try different keywords or browse our categories.</p>
                        </div>
                    `;
                } else {
                    // Display tools first
                    if (toolResults.length > 0) {
                        html += `
                            <div class="results-section">
                                <h2 class="section-title">🛠️ Tools (${toolResults.length})</h2>
                                <div class="results-grid">
                                    ${toolResults.map(tool => this.renderToolResult(tool, query)).join('')}
                                </div>
                            </div>
                        `;
                    }

                    // Display blog posts second
                    if (postResults.length > 0) {
                        html += `
                            <div class="results-section">
                                <h2 class="section-title">📝 Articles (${postResults.length})</h2>
                                <div class="results-grid">
                                    ${postResults.map(post => this.renderPostResult(post, query)).join('')}
                                </div>
                            </div>
                        `;
                    }
                }

                this.searchResults.innerHTML = html;
            }

            renderToolResult(tool, query) {
                return `
                    <div class="result-item">
                        <div class="result-title">
                            <a href="${tool.url}" target="_blank" rel="noopener">
                                ${this.highlightText(tool.title, query)}
                            </a>
                        </div>
                        <div class="result-description">
                            ${this.highlightText(tool.description, query)}
                        </div>
                        <div class="result-meta">
                            <span class="result-label">${tool.category}</span>
                            <span>🔧 Tool</span>
                        </div>
                    </div>
                `;
            }

            renderPostResult(post, query) {
                const snippet = this.createSnippet(post.content, query);
                const thumbnail = post.thumbnail ? `<img src="${post.thumbnail}" alt="${post.title}" class="result-thumbnail">` : '';
                const labels = post.labels.slice(0, 3).map(label => `<span class="result-label">${label}</span>`).join('');

                return `
                    <div class="result-item">
                        ${thumbnail}
                        <div class="result-title">
                            <a href="${post.url}" target="_blank" rel="noopener">
                                ${this.highlightText(post.title, query)}
                            </a>
                        </div>
                        <div class="result-description">
                            ${this.highlightText(snippet, query)}
                        </div>
                        <div class="result-meta">
                            ${labels}
                            <span>📝 Article</span>
                            <span>${this.formatDate(post.published)}</span>
                        </div>
                    </div>
                `;
            }

            createSnippet(content, query, maxLength = 150) {
                const text = this.stripHtml(content);
                if (text.length <= maxLength) return text;

                // Try to find the query in the text and create a snippet around it
                const queryIndex = text.toLowerCase().indexOf(query.toLowerCase());
                if (queryIndex !== -1) {
                    const start = Math.max(0, queryIndex - 50);
                    const end = Math.min(text.length, start + maxLength);
                    let snippet = text.substring(start, end);

                    if (start > 0) snippet = '...' + snippet;
                    if (end < text.length) snippet = snippet + '...';

                    return snippet;
                }

                // If query not found, just return the beginning
                return text.substring(0, maxLength) + (text.length > maxLength ? '...' : '');
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            }

            showLoading() {
                this.searchResults.innerHTML = '<div class="loading">Searching...</div>';
            }

            showInitialMessage() {
                this.searchResults.innerHTML = `
                    <div class="loading">
                        Start typing to search through our tools and articles...
                    </div>
                `;
            }
        }

        // Initialize search when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new WebToolsKitSearch();
        });

        // Update URL with search query for sharing
        function updateURL(query) {
            if (query) {
                const url = new URL(window.location);
                url.searchParams.set('q', query);
                window.history.replaceState({}, '', url);
            } else {
                const url = new URL(window.location);
                url.searchParams.delete('q');
                window.history.replaceState({}, '', url);
            }
        }
    </script>
</body>
</html>
